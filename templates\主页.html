<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>闲鱼图片审核系统</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: #f5f5f5;
      color: #333;
      line-height: 1.4;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 15px 20px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .header h1 {
      font-size: 24px;
      font-weight: 600;
    }

    .stats {
      margin-top: 8px;
      font-size: 14px;
      opacity: 0.9;
    }

    .container {
      max-width: 100%;
      padding: 20px;
    }

    .folder-section {
      background: white;
      margin-bottom: 25px;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.08);
      overflow: hidden;
      transition: transform 0.2s ease;
    }

    .folder-section:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0,0,0,0.12);
    }

    .folder-header {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: background 0.3s ease;
    }

    .folder-header:hover {
      background: linear-gradient(135deg, #43a3f5 0%, #00e8f5 100%);
    }

    .folder-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 5px;
    }

    .folder-info {
      font-size: 13px;
      opacity: 0.9;
    }

    .folder-actions {
      display: flex;
      gap: 10px;
    }

    .action-btn {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .action-btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }

    .action-btn.delete {
      background: rgba(220, 53, 69, 0.8);
      border-color: rgba(220, 53, 69, 0.9);
    }

    .action-btn.delete:hover {
      background: rgba(220, 53, 69, 1);
    }

    .action-btn.goofish {
      background: rgba(255, 165, 0, 0.8);
      border-color: rgba(255, 165, 0, 0.9);
    }

    .action-btn.goofish:hover {
      background: rgba(255, 165, 0, 1);
    }

    .images-container {
      padding: 20px;
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      align-items: flex-start;
    }

    .image-item {
      position: relative;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 3px 12px rgba(0,0,0,0.15);
      transition: all 0.3s ease;
      cursor: pointer;
      background: white;
    }

    .image-item:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 20px rgba(0,0,0,0.25);
    }

    .image-item.clickable {
      cursor: zoom-in;
    }

    .image-item img {
      width: 200px;
      height: 150px;
      object-fit: cover;
      display: block;
      transition: opacity 0.3s ease;
    }

    .image-item:hover img {
      opacity: 0.9;
    }

    .image-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0,0,0,0.8));
      color: white;
      padding: 8px 10px;
      font-size: 12px;
      transform: translateY(100%);
      transition: transform 0.3s ease;
    }

    .image-item:hover .image-overlay {
      transform: translateY(0);
    }

    .loading {
      text-align: center;
      padding: 40px;
      font-size: 16px;
      color: #666;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .empty-state h3 {
      font-size: 20px;
      margin-bottom: 10px;
    }

    .path-info {
      background: #e8f4fd;
      border: 1px solid #bee5eb;
      border-radius: 6px;
      padding: 10px 15px;
      margin-bottom: 20px;
      font-size: 13px;
      color: #0c5460;
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 6px;
      color: white;
      font-weight: 500;
      z-index: 1000;
      transform: translateX(400px);
      transition: transform 0.3s ease;
    }

    .notification.success {
      background: #28a745;
    }

    .notification.error {
      background: #dc3545;
    }

    .notification.show {
      transform: translateX(0);
    }

    /* 图片放大显示模态框 */
    .image-modal {
      display: none;
      position: fixed;
      z-index: 2000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.9);
      cursor: pointer;
    }

    .image-modal.show {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .modal-image {
      max-width: 95%;
      max-height: 95%;
      object-fit: contain;
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
      cursor: default;
    }

    .modal-close {
      position: absolute;
      top: 20px;
      right: 30px;
      color: white;
      font-size: 40px;
      font-weight: bold;
      cursor: pointer;
      z-index: 2001;
      transition: opacity 0.3s ease;
    }

    .modal-close:hover {
      opacity: 0.7;
    }

    .modal-info {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      color: white;
      background: rgba(0, 0, 0, 0.7);
      padding: 10px 20px;
      border-radius: 20px;
      font-size: 14px;
      z-index: 2001;
    }

    @media (max-width: 1200px) {
      .image-item img {
        width: 180px;
        height: 135px;
      }
    }

    @media (max-width: 768px) {
      .image-item img {
        width: 160px;
        height: 120px;
      }
    }
  </style>
</head>
<body>
  <header class="header">
    <h1>闲鱼图片审核系统</h1>
    <div class="stats">
      共 <span id="folder-count">{{ folders_data|length }}</span> 个文件夹，
      <span id="total-images">{{ folders_data|sum(attribute='image_count') }}</span> 张图片
    </div>
  </header>

  <div class="container">
    <div class="path-info">
      <strong>扫描路径:</strong> {{ base_path }}
    </div>

    {% if folders_data %}
      {% for folder in folders_data %}
      <div class="folder-section">
        <div class="folder-header">
          <div>
            <div class="folder-title">{{ folder.folder_name }}</div>
            <div class="folder-info">{{ folder.image_count }} 张图片</div>
          </div>
          <div class="folder-actions">
            <button class="action-btn" onclick="openFolder('{{ folder.folder_name }}')">
              📁 打开文件夹
            </button>
            <button class="action-btn goofish" onclick="openGoofish('{{ folder.folder_name }}')">
              🐟 访问闲鱼
            </button>
            <button class="action-btn delete" onclick="deleteFolder('{{ folder.folder_name }}')">
              🗑️ 删除文件夹
            </button>
          </div>
        </div>
        
        <div class="images-container">
          {% for image in folder.images %}
          <div class="image-item clickable" onclick="showImageModal('{{ image.url_path }}', '{{ image.name }}')">
            <img src="{{ image.url_path }}"
                 alt="{{ image.name }}"
                 onerror="this.style.display='none'" />
            <div class="image-overlay">
              {{ image.name }}
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
      {% endfor %}
    {% else %}
      <div class="empty-state">
        <h3>未找到图片文件</h3>
        <p>请检查路径是否正确，或确保文件夹中包含图片文件</p>
      </div>
    {% endif %}
  </div>

  <!-- 图片放大模态框 -->
  <div id="imageModal" class="image-modal" onclick="closeImageModal()">
    <span class="modal-close" onclick="closeImageModal()">&times;</span>
    <img id="modalImage" class="modal-image" onclick="event.stopPropagation()" />
    <div id="modalInfo" class="modal-info"></div>
  </div>

  <div id="notification" class="notification"></div>

  <script>
    function openFolder(folderName) {
      fetch(`/open_folder/${encodeURIComponent(folderName)}`)
        .then(response => response.json())
        .then(data => {
          showNotification(data.message, data.status);
        })
        .catch(error => {
          showNotification('操作失败: ' + error.message, 'error');
        });
    }

    function deleteFolder(folderName) {
      if (confirm(`确定要删除文件夹 "${folderName}" 吗？此操作不可恢复！`)) {
        fetch(`/delete_folder/${encodeURIComponent(folderName)}`)
          .then(response => response.json())
          .then(data => {
            showNotification(data.message, data.status);
            if (data.status === 'success') {
              // 刷新页面以更新文件夹列表
              setTimeout(() => {
                location.reload();
              }, 1500);
            }
          })
          .catch(error => {
            showNotification('删除失败: ' + error.message, 'error');
          });
      }
    }

    function showImageModal(imagePath, imageName) {
      const modal = document.getElementById('imageModal');
      const modalImage = document.getElementById('modalImage');
      const modalInfo = document.getElementById('modalInfo');

      modalImage.src = imagePath;
      modalInfo.textContent = imageName;
      modal.classList.add('show');

      // 防止页面滚动
      document.body.style.overflow = 'hidden';
    }

    function closeImageModal() {
      const modal = document.getElementById('imageModal');
      modal.classList.remove('show');

      // 恢复页面滚动
      document.body.style.overflow = 'auto';
    }

    function openGoofish(folderName) {
      // 构建闲鱼搜索URL
      const searchUrl = `https://www.goofish.com/search?q=${encodeURIComponent(folderName)}&spm=a21ybx.home.searchInput.0`;

      // 在新标签页中打开闲鱼搜索
      window.open(searchUrl, '_blank');

      // 显示通知
      showNotification(`正在闲鱼搜索: ${folderName}`, 'success');
    }

    function showNotification(message, type) {
      const notification = document.getElementById('notification');
      notification.textContent = message;
      notification.className = `notification ${type}`;
      notification.classList.add('show');
      
      setTimeout(() => {
        notification.classList.remove('show');
      }, 3000);
    }

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
      console.log('图片审核系统已加载');

      // 检查图片加载失败的情况
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        img.addEventListener('error', function() {
          this.parentElement.style.opacity = '0.5';
          this.parentElement.title = '图片加载失败: ' + this.alt;
        });
      });

      // 添加键盘事件监听
      document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
          closeImageModal();
        }
      });
    });
  </script>
</body>
</html>
